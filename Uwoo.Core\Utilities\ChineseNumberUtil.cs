﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Core.Utilities
{
    public static class ChineseNumberUtil
    {
        /// <summary>
        /// 将年级数字转换为中文表示（1-9为一~九，10-12为高一~高三）
        /// </summary>
        /// <param name="grade">年级数字，1-12</param>
        /// <returns>中文年级表示</returns>
        public static string ToChineseGrade(int grade)
        {
            string[] chineseNumbers = { "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            if (grade >= 1 && grade <= 9)
            {
                return chineseNumbers[grade - 1];
            }
            else if (grade == 10)
            {
                return "高一";
            }
            else if (grade == 11)
            {
                return "高二";
            }
            else if (grade == 12)
            {
                return "高三";
            }
            else
            {
                throw new ArgumentOutOfRangeException(nameof(grade), "年级必须在1到12之间");
            }
        }
    }
}
