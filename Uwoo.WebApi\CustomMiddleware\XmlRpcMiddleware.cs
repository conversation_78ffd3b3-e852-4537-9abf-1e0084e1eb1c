using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SystemIO = System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using UwooAgent.System.IServices;
using System.Xml.Linq;
using System.Reflection;
using UwooAgent.Model;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using UwooAgent.Model.RpcModel;

namespace UwooAgent.Core.Middleware
{
    /// <summary>
    /// 自定义 XML-RPC 中间件，适用于 ASP.NET Core
    /// </summary>
    public class XmlRpcMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<XmlRpcMiddleware> _logger;

        public XmlRpcMiddleware(RequestDelegate next, ILogger<XmlRpcMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUniLoginRpcService xmlRpcService)
        {
            if (context.Request.Path == "/api/rpc/sync" && context.Request.Method == "POST")
            {
                // 处理 XML-RPC 请求
                await ProcessXmlRpcRequest(context, xmlRpcService);
                return;
            }

            await _next(context);
        }

        private async Task ProcessXmlRpcRequest(HttpContext context, IUniLoginRpcService service)
        {
            try
            {
                // 设置响应头
                context.Response.ContentType = "text/xml; charset=utf-8";

                // 读取请求体
                using var reader = new SystemIO.StreamReader(context.Request.Body, Encoding.UTF8);
                var requestXml = await reader.ReadToEndAsync();

                _logger.LogInformation("XML-RPC Request: {RequestXml}", requestXml);

                // 解析 XML-RPC 请求
                var (methodName, parameters) = ParseXmlRpcRequest(requestXml);

                // 调用对应的服务方法
                var result = await InvokeServiceMethod(service, methodName, parameters);

                // 生成 XML-RPC 响应
                var responseXml = GenerateXmlRpcResponse(result);

                _logger.LogInformation("XML-RPC Response: {ResponseXml}", responseXml);

                // 写入响应
                await context.Response.WriteAsync(responseXml, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "XML-RPC Error: {Message}", ex.Message);

                // 生成错误响应
                var errorResponse = GenerateXmlRpcFault(-1, ex.Message);
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync(errorResponse, Encoding.UTF8);
            }
        }

        /// <summary>
        /// 解析 XML-RPC 请求
        /// </summary>
        private (string methodName, object[] parameters) ParseXmlRpcRequest(string requestXml)
        {
            var doc = XDocument.Parse(requestXml);
            var methodCall = doc.Element("methodCall");
            var methodName = methodCall?.Element("methodName")?.Value;

            var parameters = new List<object>();
            var paramsElement = methodCall?.Element("params");

            if (paramsElement != null)
            {
                foreach (var param in paramsElement.Elements("param"))
                {
                    var value = param.Element("value");
                    parameters.Add(ParseXmlRpcValue(value));
                }
            }

            return (methodName, parameters.ToArray());
        }

        /// <summary>
        /// 解析 XML-RPC 值
        /// </summary>
        private object ParseXmlRpcValue(XElement valueElement)
        {
            if (valueElement == null) return null;

            // 检查各种数据类型
            var stringElement = valueElement.Element("string");
            if (stringElement != null) return stringElement.Value;

            var intElement = valueElement.Element("int") ?? valueElement.Element("i4");
            if (intElement != null) return int.Parse(intElement.Value);

            var boolElement = valueElement.Element("boolean");
            if (boolElement != null) return boolElement.Value == "1";

            var structElement = valueElement.Element("struct");
            if (structElement != null)
            {
                var dict = new Dictionary<string, object>();
                foreach (var member in structElement.Elements("member"))
                {
                    var name = member.Element("name")?.Value;
                    var value = member.Element("value");
                    if (name != null)
                    {
                        dict[name] = ParseXmlRpcValue(value);
                    }
                }
                return dict;
            }

            // 如果没有明确类型，返回文本值
            return valueElement.Value;
        }

        /// <summary>
        /// 调用服务方法
        /// </summary>
        private Task<object> InvokeServiceMethod(IUniLoginRpcService service, string methodName, object[] parameters)
        {
            // 根据 methodName 映射到具体的服务方法
            object result = methodName switch
            {
                "user.updateUserCommon" => service.UpdateUser(ConvertToUniUserQo(parameters[0])),
                "user.syncUser" => service.SyncUser(ConvertToUniUserQo(parameters[0])),
                "user.addUser" => service.AddUser(ConvertToUniUserQo(parameters[0])),
                "user.deleteUser" => service.DeleteUser(parameters[0]?.ToString()),
                "group.addGroup" => service.AddGroup(ConvertToUniGroupQo(parameters[0])),
                "group.updateGroup" => service.UpdateGroup(ConvertToUniGroupQo(parameters[0])),
                "group.syncGroup" => service.SyncGroup(ConvertToUniGroupQo(parameters[0])),
                "group.deleteGroup" => service.DeleteGroup(Convert.ToInt32(parameters[0])),
                _ => throw new InvalidOperationException($"Unknown method: {methodName}")
            };

            return Task.FromResult(result);
        }

        /// <summary>
        /// 转换为 UniUserQo 对象
        /// </summary>
        private UniUserQo ConvertToUniUserQo(object parameter)
        {
            if (parameter is Dictionary<string, object> dict)
            {
                // 配置 JSON 序列化设置，使其能够正确处理 DataMember 属性
                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver
                    {
                        NamingStrategy = new Newtonsoft.Json.Serialization.DefaultNamingStrategy() // 保持原始字段名
                    },
                    NullValueHandling = NullValueHandling.Ignore, // 忽略 null 值
                    DefaultValueHandling = DefaultValueHandling.Include // 包含默认值
                };

                // 记录原始字典内容用于调试
                _logger.LogInformation("Converting dictionary to UniUserQo: {Dict}", JsonConvert.SerializeObject(dict, jsonSettings));

                // 手动映射字段，确保字段名正确对应
                var userQo = new UniUserQo();

                if (dict.TryGetValue("user_id", out var userId))
                    userQo.UserId = userId?.ToString();

                if (dict.TryGetValue("group_id", out var groupId))
                    userQo.GroupId = Convert.ToInt32(groupId);

                if (dict.TryGetValue("user_name", out var userName))
                    userQo.UserName = userName?.ToString();

                if (dict.TryGetValue("nickname", out var nickName))
                    userQo.NickName = nickName?.ToString();

                if (dict.TryGetValue("birthday", out var birthday))
                    userQo.Birthday = birthday?.ToString();

                if (dict.TryGetValue("gender", out var gender))
                    userQo.Gender = gender?.ToString();

                if (dict.TryGetValue("email", out var email))
                    userQo.Email = email?.ToString();

                if (dict.TryGetValue("active_flag", out var activeFlag))
                    userQo.ActiveFlag = Convert.ToInt32(activeFlag);

                if (dict.TryGetValue("admin_flag", out var adminFlag))
                    userQo.AdminFlag = Convert.ToInt32(adminFlag);

                if (dict.TryGetValue("user_type", out var userType))
                    userQo.UserType = userType?.ToString();

                if (dict.TryGetValue("xhjw_user_id", out var jwUserId))
                    userQo.JwUserId = Convert.ToInt32(jwUserId);

                _logger.LogInformation("Converted UniUserQo: {UserQo}", JsonConvert.SerializeObject(userQo, jsonSettings));

                return userQo;
            }
            return parameter as UniUserQo;
        }

        /// <summary>
        /// 转换为 UniGroupQo 对象
        /// </summary>
        private UniGroupQo ConvertToUniGroupQo(object parameter)
        {
            if (parameter is Dictionary<string, object> dict)
            {
                // 配置 JSON 序列化设置用于调试日志
                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver
                    {
                        NamingStrategy = new Newtonsoft.Json.Serialization.DefaultNamingStrategy()
                    },
                    NullValueHandling = NullValueHandling.Ignore,
                    DefaultValueHandling = DefaultValueHandling.Include
                };

                // 记录原始字典内容用于调试
                _logger.LogInformation("Converting dictionary to UniGroupQo: {Dict}", JsonConvert.SerializeObject(dict, jsonSettings));

                // 手动映射字段，确保字段名正确对应
                var groupQo = new UniGroupQo();

                if (dict.TryGetValue("group_id", out var groupId))
                    groupQo.GroupId = Convert.ToInt32(groupId);

                if (dict.TryGetValue("group_name", out var groupName))
                    groupQo.GroupName = groupName?.ToString();

                if (dict.TryGetValue("parent_id", out var parentId))
                    groupQo.ParentId = Convert.ToInt32(parentId);

                if (dict.TryGetValue("thread_id", out var threadId))
                    groupQo.ThreadId = Convert.ToInt32(threadId);

                if (dict.TryGetValue("group_flag", out var groupFlag))
                    groupQo.GroupFlag = Convert.ToInt32(groupFlag);

                _logger.LogInformation("Converted UniGroupQo: {GroupQo}", JsonConvert.SerializeObject(groupQo, jsonSettings));

                return groupQo;
            }
            return parameter as UniGroupQo;
        }

        /// <summary>
        /// 生成 XML-RPC 响应
        /// </summary>
        private string GenerateXmlRpcResponse(object result)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("params",
                        new XElement("param",
                            new XElement("value",
                                GenerateXmlRpcValue(result)
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }

        /// <summary>
        /// 生成 XML-RPC 值
        /// </summary>
        private XElement GenerateXmlRpcValue(object value)
        {
            return value switch
            {
                null => new XElement("string", ""),
                bool b => new XElement("boolean", b ? "1" : "0"),
                int i => new XElement("int", i.ToString()),
                string s => new XElement("string", s),
                _ => new XElement("string", value.ToString())
            };
        }

        /// <summary>
        /// 生成 XML-RPC 错误响应
        /// </summary>
        private string GenerateXmlRpcFault(int faultCode, string faultString)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("fault",
                        new XElement("value",
                            new XElement("struct",
                                new XElement("member",
                                    new XElement("name", "faultCode"),
                                    new XElement("value",
                                        new XElement("int", faultCode.ToString())
                                    )
                                ),
                                new XElement("member",
                                    new XElement("name", "faultString"),
                                    new XElement("value",
                                        new XElement("string", faultString)
                                    )
                                )
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }
    }
}
