using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SystemIO = System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using UwooAgent.System.IServices;
using System.Xml.Linq;
using System.Reflection;
using UwooAgent.Model;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace UwooAgent.Core.Middleware
{
    /// <summary>
    /// 自定义 XML-RPC 中间件，适用于 ASP.NET Core
    /// </summary>
    public class XmlRpcMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<XmlRpcMiddleware> _logger;

        public XmlRpcMiddleware(RequestDelegate next, ILogger<XmlRpcMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUniLoginRpcService xmlRpcService)
        {
            if (context.Request.Path == "/api/rpc/sync" && context.Request.Method == "POST")
            {
                // 处理 XML-RPC 请求
                await ProcessXmlRpcRequest(context, xmlRpcService);
                return;
            }

            await _next(context);
        }

        private async Task ProcessXmlRpcRequest(HttpContext context, IUniLoginRpcService service)
        {
            try
            {
                // 设置响应头
                context.Response.ContentType = "text/xml; charset=utf-8";

                // 读取请求体
                using var reader = new SystemIO.StreamReader(context.Request.Body, Encoding.UTF8);
                var requestXml = await reader.ReadToEndAsync();

                _logger.LogInformation("XML-RPC Request: {RequestXml}", requestXml);

                // 解析 XML-RPC 请求
                var (methodName, parameters) = ParseXmlRpcRequest(requestXml);

                // 调用对应的服务方法
                var result = await InvokeServiceMethod(service, methodName, parameters);

                // 生成 XML-RPC 响应
                var responseXml = GenerateXmlRpcResponse(result);

                _logger.LogInformation("XML-RPC Response: {ResponseXml}", responseXml);

                // 写入响应
                await context.Response.WriteAsync(responseXml, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "XML-RPC Error: {Message}", ex.Message);

                // 生成错误响应
                var errorResponse = GenerateXmlRpcFault(-1, ex.Message);
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync(errorResponse, Encoding.UTF8);
            }
        }

        /// <summary>
        /// 解析 XML-RPC 请求
        /// </summary>
        private (string methodName, object[] parameters) ParseXmlRpcRequest(string requestXml)
        {
            var doc = XDocument.Parse(requestXml);
            var methodCall = doc.Element("methodCall");
            var methodName = methodCall?.Element("methodName")?.Value;

            var parameters = new List<object>();
            var paramsElement = methodCall?.Element("params");

            if (paramsElement != null)
            {
                foreach (var param in paramsElement.Elements("param"))
                {
                    var value = param.Element("value");
                    parameters.Add(ParseXmlRpcValue(value));
                }
            }

            return (methodName, parameters.ToArray());
        }

        /// <summary>
        /// 解析 XML-RPC 值
        /// </summary>
        private object ParseXmlRpcValue(XElement valueElement)
        {
            if (valueElement == null) return null;

            // 检查各种数据类型
            var stringElement = valueElement.Element("string");
            if (stringElement != null) return stringElement.Value;

            var intElement = valueElement.Element("int") ?? valueElement.Element("i4");
            if (intElement != null) return int.Parse(intElement.Value);

            var boolElement = valueElement.Element("boolean");
            if (boolElement != null) return boolElement.Value == "1";

            var structElement = valueElement.Element("struct");
            if (structElement != null)
            {
                var dict = new Dictionary<string, object>();
                foreach (var member in structElement.Elements("member"))
                {
                    var name = member.Element("name")?.Value;
                    var value = member.Element("value");
                    if (name != null)
                    {
                        dict[name] = ParseXmlRpcValue(value);
                    }
                }
                return dict;
            }

            // 如果没有明确类型，返回文本值
            return valueElement.Value;
        }

        /// <summary>
        /// 调用服务方法
        /// </summary>
        private Task<object> InvokeServiceMethod(IUniLoginRpcService service, string methodName, object[] parameters)
        {
            // 根据 methodName 映射到具体的服务方法
            object result = methodName switch
            {
                "user.updateUserCommon" => service.UpdateUser(ConvertToUniUserQo(parameters[0])),
                "user.syncUser" => service.SyncUser(ConvertToUniUserQo(parameters[0])),
                "user.addUser" => service.AddUser(ConvertToUniUserQo(parameters[0])),
                "user.deleteUser" => service.DeleteUser(parameters[0]?.ToString()),
                "group.addGroup" => service.AddGroup(ConvertToUniGroupQo(parameters[0])),
                "group.updateGroup" => service.UpdateGroup(ConvertToUniGroupQo(parameters[0])),
                "group.syncGroup" => service.SyncGroup(ConvertToUniGroupQo(parameters[0])),
                "group.deleteGroup" => service.DeleteGroup(Convert.ToInt32(parameters[0])),
                _ => throw new InvalidOperationException($"Unknown method: {methodName}")
            };

            return Task.FromResult(result);
        }

        /// <summary>
        /// 转换为 UniUserQo 对象
        /// </summary>
        private UniUserQo ConvertToUniUserQo(object parameter)
        {
            if (parameter is Dictionary<string, object> dict)
            {
                var json = JsonConvert.SerializeObject(dict);
                return JsonConvert.DeserializeObject<UniUserQo>(json);
            }
            return parameter as UniUserQo;
        }

        /// <summary>
        /// 转换为 UniGroupQo 对象
        /// </summary>
        private UniGroupQo ConvertToUniGroupQo(object parameter)
        {
            if (parameter is Dictionary<string, object> dict)
            {
                var json = JsonConvert.SerializeObject(dict);
                return JsonConvert.DeserializeObject<UniGroupQo>(json);
            }
            return parameter as UniGroupQo;
        }

        /// <summary>
        /// 生成 XML-RPC 响应
        /// </summary>
        private string GenerateXmlRpcResponse(object result)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("params",
                        new XElement("param",
                            new XElement("value",
                                GenerateXmlRpcValue(result)
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }

        /// <summary>
        /// 生成 XML-RPC 值
        /// </summary>
        private XElement GenerateXmlRpcValue(object value)
        {
            return value switch
            {
                null => new XElement("string", ""),
                bool b => new XElement("boolean", b ? "1" : "0"),
                int i => new XElement("int", i.ToString()),
                string s => new XElement("string", s),
                _ => new XElement("string", value.ToString())
            };
        }

        /// <summary>
        /// 生成 XML-RPC 错误响应
        /// </summary>
        private string GenerateXmlRpcFault(int faultCode, string faultString)
        {
            var doc = new XDocument(
                new XDeclaration("1.0", "utf-8", null),
                new XElement("methodResponse",
                    new XElement("fault",
                        new XElement("value",
                            new XElement("struct",
                                new XElement("member",
                                    new XElement("name", "faultCode"),
                                    new XElement("value",
                                        new XElement("int", faultCode.ToString())
                                    )
                                ),
                                new XElement("member",
                                    new XElement("name", "faultString"),
                                    new XElement("value",
                                        new XElement("string", faultString)
                                    )
                                )
                            )
                        )
                    )
                )
            );

            return doc.ToString();
        }
    }
}
