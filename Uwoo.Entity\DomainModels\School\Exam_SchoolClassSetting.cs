﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.School
{
    /// <summary>
    /// 
    /// </summary>
    public class Exam_SchoolClassSetting
    {

        /// <summary>
        /// Id
        /// </summary>
        [Key, Column(Order = 1)]
        [SugarColumn(IsPrimaryKey = true)]
        public String Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 否已删除
        /// </summary>
        public Boolean Deleted { get; set; }

        /// <summary>
        /// 所属学校
        /// </summary>
        public String SchoolId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public String Name { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public Int32? Grade { get; set; }

        /// <summary>
        /// 班
        /// </summary>
        public Int32? Class { get; set; }

        /// <summary>
        /// 统一用户班级Id
        /// </summary>
        public string TYId { get; set; }

        /// <summary>
        /// 统一用户班级Id
        /// </summary>
        public string HpId { get; set; }

        /// <summary>
        /// 微校网校班级认证 Id
        /// </summary>
        public string WxId { get; set; }

        /// <summary>
        /// 徐汇网校认证Id
        /// </summary>
        public string XhId { get; set; }

    }
}
