using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UwooAgent.Model;
using UwooAgent.Model.RpcModel;

namespace UwooAgent.System.IServices
{
    /// <summary>
    /// 统一登录 RPC 服务接口
    /// 注意：方法名映射在 XmlRpcMiddleware 中定义
    /// </summary>
    public interface IUniLoginRpcService
    {
        /// <summary>
        /// 更新用户信息 (XML-RPC: user.updateUserCommon)
        /// </summary>
        bool UpdateUser(UniUserQo qo);

        /// <summary>
        /// 同步用户信息 (XML-RPC: user.syncUser)
        /// </summary>
        bool SyncUser(UniUserQo qo);

        /// <summary>
        /// 添加用户 (XML-RPC: user.addUser)
        /// </summary>
        bool AddUser(UniUserQo qo);

        /// <summary>
        /// 删除用户 (XML-RPC: user.deleteUser)
        /// </summary>
        bool DeleteUser(string userid);

        /// <summary>
        /// 添加分组 (XML-RPC: group.addGroup)
        /// </summary>
        bool AddGroup(UniGroupQo qo);

        /// <summary>
        /// 更新分组 (XML-RPC: group.updateGroup)
        /// </summary>
        bool UpdateGroup(UniGroupQo qo);

        /// <summary>
        /// 同步分组 (XML-RPC: group.syncGroup)
        /// </summary>
        bool SyncGroup(UniGroupQo qo);

        /// <summary>
        /// 删除分组 (XML-RPC: group.deleteGroup)
        /// </summary>
        bool DeleteGroup(int id);
    }
}
