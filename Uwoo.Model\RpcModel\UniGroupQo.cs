﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace UwooAgent.Model
{
    public class UniGroupQo
    {
        /// <summary>
        /// GroupId
        /// </summary>
        [JsonProperty("group_id")]
        public int? GroupId { get; set; }

        /// <summary>
        /// GroupName
        /// </summary>
        [JsonProperty("group_name")]
        public string GroupName { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        [JsonProperty("parent_id")]
        public int? ParentId { get; set; }

        /// <summary>
        /// ThreadId
        /// </summary>
        [JsonProperty("thread_id")]
        public int? ThreadId { get; set; }

        /// <summary>
        ///  (组标记，0: 普通组，1: 学校，2: 年级，3: 班级)
        /// </summary>
        [JsonProperty("group_flag")]
        public int? GroupFlag { get; set; }
    }
}
