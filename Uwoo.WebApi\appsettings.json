{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "VirtualPath": {
    "StaticFile": "E:\\Web\\Static", //配置的虚拟目录文件所在路径
    "FolderName": "/Static" //访问时此路径时的别名
  },
  "AppUrls": {

  },
  "HuaweiObs": {
    "AK": "HPUAOQMESUS0ILVEMYMY",
    "SK": "fHCnZ9JELFifmU9WZaEka4GoH1UERsskxr586qA7",
    "Endpoint": "https://obs.cn-east-2.myhuaweicloud.com"
  },
  "Upload": {
    "OfficeToolUrl": "http://appapi.eduwon.cn/OfficeTool",
    "PuMaUrl": "https://uwooapi.test.eduwon.cn",
    "WpsUploadUrl": "https://uwooapi.test.eduwon.cn",
    "UwooWebSocket": ""
  },
  "WPSConfig": {
    "AppId": "AK20231204OEUSCL",
    "AppSecretKey": "cxkEtCORqQowWxskDRJUKbYnEugkHckG"
  },
  "JPushConfig": {
    "AppKey": "003287d6d3760b235dd21641",
    "AppSecret": "f6968929fe66e2e3a00857eb"
  },
  "Connection": {
    "DBType": "MsSql", //MySql/MsSql/PgSql  //数据库类型，如果使用的是sqlserver此处应设置为MsSql
    //sqlserver加密连接字符串
    "DbConnectionString": "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;",

    //"DbConnectionString": "Server=*************;Database=YouwoEduPlatfrom;User ID=sa_dev;Password=*************;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=false;TrustServerCertificate=True;",

    //mysql连接字符串(升级EFCore3.1到时已将mysql连接字符串修改,2019-12-20)
    // "DbConnectionString": " Data Source=127.0.0.1;Database=netcoredev;AllowLoadLocalInfile=true;User ID=root;Password=******;allowPublicKeyRetrieval=true;pooling=true;CharSet=utf8;port=3306;sslmode=none;",

    //PgSql连接字符串
    //  "DbConnectionString": "Host=*************;Port=5432;User id=postgres;password=********;Database=netcoredev;",
    "RedisConnectionString": "***************:6379,password=ABcd?******,SyncTimeout=15000", //redis连接字符串(最好加密)
    "UseRedis": "true", //是否使用redis，如果不使用，默认使用Memory内置缓存
    "UseSignalR": "false" //是否使用SignalR(2022.05.03)，注意需要将端的地址配置到下面的CorsUrls属性中
  },
  "Secret": { //秘钥配置
    "JWT": "BB3647441FFA4B5DB4E64A29B53CE525", //JWT
    "Audience": "Uwoo.Core",
    "Issuer": "Uwoo.Core.owner",
    "User": "C5ABA9E202D94C43A3CA66002BF77FAF", //
    "DB": "3F8B7B38AD3D484A",
    "Redis": "E6D90DDBC70C4F4EA3C312B6FCB473C8"
  },
  "RabbitMQ": {
    "HostName": "***************",
    "UserName": "uwoo",
    "Password": "uwoo@123",
    "Port": 5672,
    "Heartbeat": 20,
    "VirtualHost": "/"
  },
  "XmlRpcOptions": {
    "GenerateSummary": "true",
    "SummaryEndpoint": "/api/rpc/summary",
    "RsdEndpoint": "/api/rpc/rsd",
    "Endpoint": "/api/rpc/endpoint",
    "EngineName": "X.XmlRpc",
    "TokenName": "lucifer",
    "ManifestEndpoint": "/api/rpc/manifest",
    "UserName": "eduwon",
    "Password": "Uwoo@123",
    "EnableCredentials": 1
  },
  "XmlRpcClientOptions": {
    "BaseUrl": "https://platform.xhedu.sh.cn/platform/rpc/",
    "UserName": "K12RPC",
    "Password": "K12RPCPwd",
    "CallbackUrl": "https://agentapi.eduwon.cn/Login/Login/xhunilogin",
    "LoginUrl": "https://platform.xhedu.sh.cn/platform/app/login.php"
  },
  "DouBaoAI": {
    "AccessKey": "AKLTYmExZWNhMzhhZTg2NDYzOGJhNGYzYzViMjIwMGEyYTc",
    "SecertAccessKey": "TXpZelpXWmpObUV4Tm1Fek5EbGxNRGt3TkRaaE16VmxabUkxT0dRNVltWQ==",
    "APIKey": "Bearer 7c47b780-c644-45ad-b13f-4c1efc54e0a6", //API Key
    "AIGenerateImageUrl": "https://ark.cn-beijing.volces.com/api/v3/images/generations", //AI生成图片地址
    "AIGenerateImageModelId": "ep-20250714170643-269bg", //AI生成图片模型Id
    "AIGenerateHTMLCodeUrl": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", //AI生成HTML代码地址
    "AIGenerateQuestionsUrl": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", //AI生成试题地址
    "AIGenerateHTMLCodeModeId": "ep-20250715095755-qvmpq", //AI生成HTML代码模型Id
    "CreateContextUrl": "https://ark.cn-beijing.volces.com/api/v3/context/create", //创建上下文缓存地址
    "ContextDialogueUrl": "https://ark.cn-beijing.volces.com/api/v3/context/chat/completions", //上下文对话地址
    "VoiceAppId": "7668498407", //语音技术APPId
    "VoiceToken": "0zGRXcMI4CI07_gp5_AsNr5WvA4jmFuL", //语音技术Token
    "AudioFileChangeTextUrl": "https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash", //语音文件转文本地址,
    "TextChangeVoiceUrl": "wss://openspeech.bytedance.com/api/v3/tts/bidirection", //文本转语音地址
    "DialogueUrl": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", //对话地址
    "MyAppUrl": "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions", //我的应用地址
    "FileAnalysisAppId": "bot-20250624163429-x2962", //文件分析应用Id
    "KnowLedgeBaseUrl": "https://api-knowledgebase.mlp.cn-beijing.volces.com" //豆包知识库地址
  },
  "WenDuoDuo": {
    "ApiKey": "ak_rJBmez5p5s5FsxA6OJ", //ApiKey
    "CreateTokenUrl": "https://open.docmee.cn/api/user/createApiToken", //创建tokne地址
    "DownloadPPTUrl": "https://open.docmee.cn/api/ppt/downloadPptx" //下载PPT地址
  },
  "OfficeTool": {
    "OfficeToolUrl": "http://127.0.0.1:5007/", //OfficeTool地址
    "MarkdownTextToWordUrl": "OfficeTool/MarkdownTextToWord", //Markdown文本转word地址
    "MarkdownTextToWordZipUrl": "OfficeTool/MarkdownTextToWordZip" //Markdown文本转word生成压缩包地址
  },
  "MongoConfig": {
    // "ConnectionString": "mongodb://root:Uwoo%40123!%40#@***************:27017/?directConnection=true",
    // "DatabaseName": "UwooPenMongo"
    "ConnectionString": "mongodb://Lucifer:Uwoo%40123!%40#@**************:27017/?directConnection=true",
    "DatabaseName": "UwooPenMongo"
  },
  //================跨域请求 (CORS)配置(2019-12-20新增)，
  //================.netcore3.1必须配置此属性,多个url用豆号隔开,url为vue站点的地址，可以将发布后的地址也同时配置上
  "CorsUrls": "http://localhost:8081,http://localhost:8080,http://localhost:7080,http://localhost:9980,http://127.0.0.1:9980,http://localhost:9990,http://www.volcore.xyz",
  "ExpMinutes": "120", //JWT有效期(分钟=默认120),
  "CreateMember": { //对表插入数据时，需要记录创建人/创建时间/创建日期,配置UserIdField/UserNameField/DateField分别为对应数据库的创建人CreateID,创建人Creator,创建时间CreateDate字段(新建数据时,由框架默认完成给这几个字段赋值,字段区分大小写)或可手动调用T.SetCreateDefaultVal()完成设置创建人/创建时间/创建日期
    //如果表的主键是GUID，界面查询时默认会用到DateField对应的实体(数据库)字段进行排序
    "UserIdField": "CreateID",
    "UserNameField": "Creator",
    "DateField": "CreateDate"
  },
  "ModifyMember": { //修改同上
    "UserIdField": "ModifyID",
    "UserNameField": "Modifier",
    "DateField": "ModifyDate"
  }, //演示系统过滤Action，只有超级管理员才能操作，其他用户只有只读权限
  "GlobalFilter": {
    "Message": "演示环境,当前帐号没有开启此功能权限",
    "Enable": "false", //开启Action过滤
    "Actions": [ "Update", "Del", "Add", "SavePermission", "Save", "CreatePage", "CreateVuePage", "CreateEntityModel", "SaveEidt", "CreateServices", "Import", "Upload", "Audit", "ModifyPwd" ]
  },
  "Kafka": {
    //是否使用生产者
    "UseProducer": false,
    "ProducerSettings": {
      "BootstrapServers": "**************:9092", //confluent cloud bootstrap servers
      "SaslMechanism": "Plain",
      "SecurityProtocol": "SaslSsl",
      "SaslUsername": "<confluent cloud key>",
      "SaslPassword": "<confluent cloud secret>"
    },
    //是否使用消费者
    "UseConsumer": false,
    //是否持续监听消费者订阅 用于while循环订阅
    "IsConsumerSubscribe": false,
    "ConsumerSettings": {
      "BootstrapServers": "**************:9092", //confluent cloud bootstrap servers
      "GroupId": "amcl_group", //web-example-group
      "SaslMechanism": "Plain",
      "SecurityProtocol": "SaslSsl",
      "SaslUsername": "<confluent cloud key>",
      "SaslPassword": "<confluent cloud secret>"
    },
    "Topics": {
      "TestTopic": "alarm_topic"
    }
  },
  "Mail": {
    "Address": "<EMAIL>", //发件的邮箱
    "Host": "smtp.163.com",
    "Name": "Uwoo", //发送人名称
    "Port": 25,
    "EnableSsl": false,
    "AuthPwd": "授权密码" //授权密码（对应邮箱设置里面去开启）
  },
  "QuartzAccessKey": "65EC9387355E4717899C552963CE59FF", //定时任务的值,请自行修改
  "ShowSqlLog": "1", //是否显示执行的sql
  "AnswerBox": "data:image/png;base64,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",
  "PaperCenter": {
    "PaperCenterUrl": "http://127.0.0.1:9001" //试卷中心地址
  },
  "DataCenter": {
    "Enabled": true,
    "Domain": "http://127.0.0.1:9291/"
  },
  "CKDataCenter": {
    "Enabled": false,
    "Domain": "http://192.168.110.191:8090/"
  },
  "Coze": {
    "WorkflowUrl": "https://api.coze.cn/v1/workflow/stream_run",
    "TeachingPlanAnalysis": {
      "Token": "Bearer pat_XTVkevXcgrcrwa9mi3oKixfntobrQbKq9GEQu2UE10Kk4FqAiJPGJkZbpz1kk1Ek",
      "WorkflowId": "7538278065873846322"
    }
  }
}
