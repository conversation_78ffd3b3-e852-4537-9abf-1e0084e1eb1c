<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UwooAgent.WebApi</name>
    </assembly>
    <members>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentCommonController">
            <summary>
            智能体_通用功能
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIUploadFile(Microsoft.AspNetCore.Http.IFormFile[])">
            <summary>
            AI上传文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIGenerateImage(UwooAgent.Model.AI.AIGenerateImageInput)">
            <summary>
            AI_生成图片
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIGenerateHTMLCode(UwooAgent.Model.AI.AIGenerateHTMLCodeInput)">
            <summary>
            AI_生成HTML代码
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAIFileInfoList">
            <summary>
            获取AI文件消息
            </summary>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AudioFileChangeText(UwooAgent.Model.AI.AudioFileChangeTextInput)">
            <summary>
            音频文件转文本
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.TextChangeVoice(UwooAgent.Model.AI.AITextChangeVoiceInput,System.Threading.CancellationToken)">
            <summary>
            文本转语音
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAIDialogueContentRecord(UwooAgent.Model.AI.GetAIDialogueContentRecordInput)">
            <summary>
            获取AI对话内容记录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAgentModelInfo">
            <summary>
            获取智能体模型信息
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetToneBaseInfo">
            <summary>
            获取音色信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.PPTCreateLimitOfTimes(UwooAgent.Model.AI.PPTCreateLimitOfTimesInput)">
            <summary>
            PPT创建次数限制验证
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.SavePPTCreateLimitOfTimes(UwooAgent.Model.AI.SavePPTCreateLimitOfTimesInput)">
            <summary>
            保存PPT创建次数
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetCreatePPTWeDuoDuoToken(UwooAgent.Model.AI.GetCreatePPTWeDuoDuoTokenInput)">
            <summary>
            获取创建PPT文多多token
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.SaveFileAnalysisResult(UwooAgent.Model.AI.SaveFileAnalysisInput)">
            <summary>
            文件分析（保存分析结果）
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.DelFileAnalysisResult(UwooAgent.Model.AI.DelFileAnalysisResultInput)">
            <summary>
            删除文件分析结果
            </summary>
            <param name="input">文件Id</param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController">
            <summary>
            智能体_智能出题
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetQuestionTypes">
            <summary>
            获取题型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetDifficultyLevels">
            <summary>
            获取难度等级列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetQuestionDirections">
            <summary>
            获取出题方向列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetKnowledgePointsBySubject(System.String,System.Nullable{System.Int32})">
            <summary>
            根据学科获取知识点列表
            </summary>
            <param name="subjectId">学科ID</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GenerateQuestions(UwooAgent.Model.AI.IntelligentQuestionGenerationInput)">
            <summary>
            智能出题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GenerateQuestionsStream(UwooAgent.Model.AI.IntelligentQuestionGenerationInput,System.Threading.CancellationToken)">
            <summary>
            智能出题（SSE流式返回）
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.RegenerateQuestion(UwooAgent.Model.AI.RegenerateQuestionInput)">
            <summary>
            重新生成单题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.SaveSingleQuestionToBank(UwooAgent.Model.AI.SaveSingleQuestionToBankInput)">
            <summary>
            单题保存到题库
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.SaveBatchQuestionsToBank(UwooAgent.Model.AI.SaveBatchQuestionsToBankInput)">
            <summary>
            批量保存到题库
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentHomePageController">
            <summary>
            智能体_学生端首页控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentHomePageController.GetStudentHomePageAgentTaskList(UwooAgent.Model.AI.AgentStudentHomePageAgentTaskInput)">
            <summary>
            智能体_学生端首页智能体任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController">
            <summary>
            智能体_学生端建模
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.GetStudentModelingInfo(UwooAgent.Model.AI.GetStudentModelingInfoInput)">
            <summary>
            获取学生端建模信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentReadModelingDocument(UwooAgent.Model.AI.StudentReadModelingDocumentInput)">
            <summary>
            学生阅读建模文档
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentWatchModelingVideo(UwooAgent.Model.AI.StudentWatchModelingVideoInput)">
            <summary>
            学生观看建模视频
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingAssess(UwooAgent.Model.AI.StudentModelingAssessInput)">
            <summary>
            学生端建模作品评估
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingDialogue(UwooAgent.Model.AI.StudentModelingDialogueInput,System.Threading.CancellationToken)">
            <summary>
            学生端建模情景对话
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingDialogueSubmit(UwooAgent.Model.AI.StudentModelingDialogueSubmitInput)">
            <summary>
            学生端建模情景对话提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingKnowledge(UwooAgent.Model.AI.StudentModelingKnowledgeInput,System.Threading.CancellationToken)">
            <summary>
            学生端建模知识问答
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingComprehend(UwooAgent.Model.AI.StudentModelingComprehendInput,System.Threading.CancellationToken)">
            <summary>
            学生端建模问题理解
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingComprehendSubmit(UwooAgent.Model.AI.StudentModelingComprehendSubmitInput)">
            <summary>
            学生端建模问题理解提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingStructureImgOcr(UwooAgent.Model.AI.StudentModelingStructureImgOcrInput)">
            <summary>
            学生端建模构建图片识别
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingStructureSubmit(UwooAgent.Model.AI.StudentModelingStructureSubmitInput)">
            <summary>
            学生端建模模型构建提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingHypothesis(UwooAgent.Model.AI.StudentModelingHypothesisInput,System.Threading.CancellationToken)">
            <summary>
            学生端建模模型假设
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingHypothesisSubmit(UwooAgent.Model.AI.StudentModelingHypothesisSubmitInput)">
            <summary>
            学生端建模模型假设提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingEvaluate(UwooAgent.Model.AI.StudentModelingEvaluateInput,System.Threading.CancellationToken)">
            <summary>
            学生端建模模型评价
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingEvaluateSubmit(UwooAgent.Model.AI.StudentModelingEvaluateSubmitInput)">
            <summary>
            学生端建模模型评价提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingStructureNewest(UwooAgent.Model.AI.StudentModelingStructureNewestInput)">
            <summary>
            获取学生模型构建最新版本模型构建信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingStructureOptimize(UwooAgent.Model.AI.StudentModelingStructureOptimizeInput)">
            <summary>
            学生端建模模型构建优化
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingDialogueRecord(UwooAgent.Model.AI.StudentModelingDialogueRecordInput)">
            <summary>
            获取学生建模相关任务对话内容记录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingStructureRecord(UwooAgent.Model.AI.StudentModelingStructureRecordInput)">
            <summary>
            获取学生建模模型构建记录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.StudentModelingSubmitNoStandardBackups(UwooAgent.Model.AI.StudentModelingSubmitNoStandardBackupsInput)">
            <summary>
            学生端建模提交未达标对话记录备份
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.GetStudentDoModelingTaskResult(UwooAgent.Model.AI.GetStudentDoModelingTaskResultInput)">
            <summary>
            获取学生做建模阶段任务结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentModelingController.GetStudentDoModelingNoStandardList(UwooAgent.Model.AI.GetStudentDoModelingNoStandardListInput)">
            <summary>
            获取学生做建模未达标列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController">
            <summary>
            智能体_学生端语交际
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController.AgentStudentOralCommunicationDialogue(UwooAgent.Model.AI.AgentStudentOralCommunicationDialogueInput,System.Threading.CancellationToken)">
            <summary>
            智能体_学生端口语交际对话
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController.AgentStudentOralCommunicationSubmit(UwooAgent.Model.AI.AgentStudentOralCommunicationSubmitInput)">
            <summary>
            智能体_学生端口语交际提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController">
            <summary>
            智能体_学生端项目化实践
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.GetStudentProjectInfo(UwooAgent.Model.AI.GetStudentProjectInfoInput)">
            <summary>
            获取学生端项目化实践信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.StudentAssess(UwooAgent.Model.AI.StudentAssessInput)">
            <summary>
            学生端作品评估
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.StudentDialogue(UwooAgent.Model.AI.StudentDialogueInput,System.Threading.CancellationToken)">
            <summary>
            学生端情景对话
            </summary>
            <param name="input"></param>
            <param name="dataHandler"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.StudentDialogueSubmit(UwooAgent.Model.AI.StudentDialogueSubmitInput)">
            <summary>
            学生端情景对话提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.StudentKnowledge(UwooAgent.Model.AI.StudentKnowledgeInput,System.Threading.CancellationToken)">
            <summary>
            学生端知识问答
            </summary>
            <param name="input"></param>
            <param name="dataHandler"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.StudentSubmitNoStandardBackups(UwooAgent.Model.AI.StudentSubmitNoStandardBackupsInput)">
            <summary>
            学生端项目化实践阶段任务提交未达标对话记录备份
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.GetStudentDoTaskResult(UwooAgent.Model.AI.GetStudentDoTaskResultInput)">
            <summary>
            获取学生做项目化实践阶段任务结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentProjectController.GetStudentNoStandardList(UwooAgent.Model.AI.GetStudentNoStandardListInput)">
            <summary>
            获取学生端未达标列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController">
            <summary>
            智能体_教师首页控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController.GetAgentListInfo(UwooAgent.Model.AI.AgentTeacherHomePageListInput)">
            <summary>
            获取智能体列表信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController.AgentCollection(UwooAgent.Model.AI.AgentTeacherCollectionInput)">
            <summary>
            智能体收藏
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController">
            <summary>
            智能体_教师端建模
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.SaveModelingInfo(UwooAgent.Model.AI.SaveModelingInfoInput)">
            <summary>
            保存/编辑建模信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingStageTaskDefaultInfo">
            <summary>
            获取建模阶段任务默认信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingInfo(UwooAgent.Model.AI.GetModelingInfoInput)">
            <summary>
            获取建模信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.DeleteModelingInfo(UwooAgent.Model.AI.DeleteModelingInfoInput)">
            <summary>
            删除建模信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.PublishModelingInfo(UwooAgent.Model.AI.PublishModelingInfoInput)">
            <summary>
            发布建模信息
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.UnpublishModelingInfo(UwooAgent.Model.AI.UnpublishModelingInfoInput)">
            <summary>
            撤销建模发布
            </summary>
            <param name="input">撤销发布参数</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingList(UwooAgent.Model.AI.GetModelingListInput)">
            <summary>
            获取建模列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingSynthesizeAnalyse(UwooAgent.Model.AI.GetModelingSynthesizeAnalyseInput)">
            <summary>
            获取建模综合分析
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingStageTaskCount(UwooAgent.Model.AI.GetModelingStageTaskCountInput)">
            <summary>
            获取建模阶段任务统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetModelingStudentCount(UwooAgent.Model.AI.GetModelingStudentCountInput)">
            <summary>
            获取建模学生统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.GetStudentDoModelingTaskList(UwooAgent.Model.AI.GetStudentDoModelingTaskListInput)">
            <summary>
            获取学生做建模任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.ModelingDownloadStudentAchievement(UwooAgent.Model.AI.ModelingDownloadStudentAchievementInput)">
            <summary>
            教师端建模下载学生成果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherModelingController.ModelingDownloadStudentResult(UwooAgent.Model.AI.ModelingDownloadStudentResultInput)">
            <summary>
            教师端建模下载学生评估结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController">
            <summary>
            智能体_教师口语交际
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.SaveTeacherOralCommunication(UwooAgent.Model.AI.SaveTeacherOralCommunicationInput)">
            <summary>
            教师保存/编辑口语交际
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationDetail(UwooAgent.Model.AI.AgentOralCommunicationDetailInput)">
            <summary>
            获取智能体口语交际详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.DelOralCommunication(UwooAgent.Model.AI.DelOralCommunicationInput)">
            <summary>
            删除口语交际任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationList(UwooAgent.Model.AI.AgentTeacherOralCommunicationListInput)">
            <summary>
            智能体_教师端口语交际列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationAnalyse(UwooAgent.Model.AI.GetOralCommunicationAnalyseInput)">
            <summary>
            智能体_教师端获取智能体任务分析
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationStudentResult(UwooAgent.Model.AI.GetOralCommunicationStudentResultInput)">
            <summary>
            教师获取学生口语交际评估结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.PublishOralCommunication(UwooAgent.Model.AI.PublishOralCommunicationInput)">
            <summary>
            发布口语交际
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.DelPublishOralCommunication(UwooAgent.Model.AI.DelPublishOralCommunicationInput)">
            <summary>
            撤销发布口语交际
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController">
            <summary>
            智能体_教师端项目化实践
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.SaveProjectTaskInfo(UwooAgent.Model.AI.SaveProjectTaskInfoInput)">
            <summary>
            保存/编辑项目化实践任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectTaskDetails(UwooAgent.Model.AI.ProjectTaskDetailsInput)">
            <summary>
            获取项目化实践详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.DelProjectTask(UwooAgent.Model.AI.DelProjectTaskInput)">
            <summary>
            删除项目化实践任务
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.PublishProjectTaskToClass(UwooAgent.Model.AI.PublishProjectTaskToClassInput)">
            <summary>
            发布项目化实践任务到班级
            </summary>
            <param name="input">发布参数</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.UnpublishProjectTask(UwooAgent.Model.AI.UnpublishProjectTaskInput)">
            <summary>
            撤销项目化实践任务发布
            </summary>
            <param name="input">撤销发布参数</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectTaskList(UwooAgent.Model.AI.GetProjectTaskListInput)">
            <summary>
            获取项目化实践列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.AICreateTaskProperty(UwooAgent.Model.AI.AICreateTaskPropertyInput,System.Threading.CancellationToken)">
            <summary>
            AI创建项目化实践阶段任务相关属性
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetStudentDoProjectTaskList(UwooAgent.Model.AI.GetStudentDoProjectTaskListInput)">
            <summary>
            获取学生做任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectSynthesizeAnalyse(UwooAgent.Model.AI.GetProjectSynthesizeAnalyseInput)">
            <summary>
            获取项目化实践综合分析
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectStageTaskCount(UwooAgent.Model.AI.GetProjectStageTaskCountInput)">
            <summary>
            获取项目化实践阶段任务统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectStudentCount(UwooAgent.Model.AI.GetProjectStudentCountInput)">
            <summary>
            获取项目化实践学生统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController">
            <summary>
            智能体_教师端教案
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.SaveTeachingPlanContentDemand(UwooAgent.Model.AI.SaveTeachingPlanContentDemandInput)">
            <summary>
            保存教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.DelTeachingPlanContentDemand(UwooAgent.Model.AI.DelTeachingPlanContentDemandInput)">
            <summary>
            删除教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanContentDemand(UwooAgent.Model.AI.GetTeachingPlanContentDemandInput)">
            <summary>
            获取教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.CreateTeachingPlan(UwooAgent.Model.AI.CreateTeachingPlanInput,System.Threading.CancellationToken)">
            <summary>
            创建教案
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.UpdateTeachingPlanCreateRecord(UwooAgent.Model.AI.UpdateTeachingPlanCreateRecordInput)">
            <summary>
            修改教案创建记录中的教案文本
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanCreateRecord(UwooAgent.Model.AI.GetTeachingPlanCreateRecordInput)">
            <summary>
            获取教案创建记录列表
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordOptimize(UwooAgent.Model.AI.TeachingPlanCreateRecordOptimizeInput,System.Threading.CancellationToken)">
            <summary>
            教案创建记录文本润色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordDetails(UwooAgent.Model.AI.TeachingPlanCreateRecordDetailsInput)">
            <summary>
            教案创建记录详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordTextToWord(UwooAgent.Model.AI.TeachingPlanCreateRecordTextToWordInput)">
            <summary>
            教案生成记录文本转word
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordFileUpdateName(UwooAgent.Model.AI.TeachingPlanCreateRecordFileUpdateNameInput)">
            <summary>
            教案生成记录修改文件名称
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordFile(UwooAgent.Model.AI.TeachingPlanCreateRecordFileInput)">
            <summary>
            获取教案生成记录文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.SaveTeachingPlan(UwooAgent.Model.AI.SaveTeachingPlanInput)">
            <summary>
            保存教案
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlan(UwooAgent.Model.AI.GetTeachingPlanInput)">
            <summary>
            获取教案列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanDetails(UwooAgent.Model.AI.GetTeachingPlanDetailsInput)">
            <summary>
            获取教案详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanTextUpdate(UwooAgent.Model.AI.TeachingPlanTextUpdateInput)">
            <summary>
            教案文本类型编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanUpdateName(UwooAgent.Model.AI.TeachingPlanUpdateNameInput)">
            <summary>
            教案重命名
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.DelTeachingPlan(UwooAgent.Model.AI.DelTeachingPlanInput)">
            <summary>
            删除教案
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController">
            <summary>
            教案相关接口
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController.GetTeachingPlanLibrary(System.String,System.String,System.String)">
            <summary>
            获取教案库列表
            </summary>
            <returns>教案库列表</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController.SelectTeachingPlan(UwooAgent.Model.AI.AgentTeachingPlanAnalysis.TeachingPlanSelectionModel)">
            <summary>
            选择教案 (废弃)
            </summary>
            <param name="model">教案选择模型</param>
            <returns>选择结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController.GetStudentPaperList(UwooAgent.Model.AI.AgentTeachingPlanAnalysis.TeacherClassPaperInput)">
            <summary>
            获取专课专练作业列表
            </summary>
            <returns>作业列表</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController.StudentsGradeDistribution(UwooAgent.Model.AI.AgentTeachingPlanAnalysis.StudentGradeDistributionInput)">
            <summary>
            学生成绩分布情况
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeachingPlanAnalysisController.GenerateTeachingPlanAnalysis(UwooAgent.Model.AI.AgentTeachingPlanAnalysis.GenerateTeachingPlanAnalysisInput)">
            <summary>
            生成教案分析
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AIDirectiveController">
            <summary>
            AI指令管理控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetList(UwooAgent.Model.AI.AIDirectiveQueryInput)">
            <summary>
            获取AI指令分页列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetDetail(System.String)">
            <summary>
            根据ID获取AI指令详情
            </summary>
            <param name="id">指令ID</param>
            <returns>指令详情</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Create(UwooAgent.Model.AI.CreateAIDirectiveInput)">
            <summary>
            创建AI指令
            </summary>
            <param name="input">创建参数</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Update(UwooAgent.Model.AI.UpdateAIDirectiveInput)">
            <summary>
            更新AI指令
            </summary>
            <param name="input">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Delete(System.String)">
            <summary>
            删除AI指令（软删除）
            </summary>
            <param name="id">指令ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Restore(System.String)">
            <summary>
            恢复已删除的AI指令
            </summary>
            <param name="id">指令ID</param>
            <returns>恢复结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetTypeStatistics">
            <summary>
            获取AI指令类型统计
            </summary>
            <returns>类型统计</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Search(System.String,System.Int32)">
            <summary>
            搜索AI指令
            </summary>
            <param name="keyword">搜索关键词</param>
            <param name="limit">返回数量限制</param>
            <returns>搜索结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.BatchDelete(System.String[])">
            <summary>
            批量删除AI指令
            </summary>
            <param name="ids">指令ID列表</param>
            <returns>批量删除结果</returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.KnowledgeController">
            <summary>
            知识库管理控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetList(UwooAgent.Model.AI.KnowledgeQueryInput)">
            <summary>
            获取知识库分页列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetDetail(System.String)">
            <summary>
            根据ID获取知识库详情
            </summary>
            <param name="id">知识库ID</param>
            <returns>知识库详情</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Create(UwooAgent.Model.AI.CreateKnowledgeInput)">
            <summary>
            创建知识库
            </summary>
            <param name="input">创建参数</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Update(UwooAgent.Model.AI.UpdateKnowledgeInput)">
            <summary>
            更新知识库
            </summary>
            <param name="input">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Delete(System.String)">
            <summary>
            删除知识库
            </summary>
            <param name="id">知识库ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.CheckName(System.String,System.String)">
            <summary>
            检查知识库名称是否存在
            </summary>
            <param name="name">知识库名称</param>
            <param name="excludeId">排除的ID（用于更新时检查）</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetStatistics">
            <summary>
            获取知识库统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.Base.SemesterTimeController">
            <summary>
            学年学期控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.Base.SemesterTimeController.GetNowYearSemesterTime">
            <summary>
            获取当前学年学期时间
            </summary>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.Core.Middleware.XmlRpcMiddleware">
            <summary>
            自定义 XML-RPC 中间件，适用于 ASP.NET Core
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.ParseXmlRpcRequest(System.String)">
            <summary>
            解析 XML-RPC 请求
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.ParseXmlRpcValue(System.Xml.Linq.XElement)">
            <summary>
            解析 XML-RPC 值
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.InvokeServiceMethod(UwooAgent.System.IServices.IUniLoginRpcService,System.String,System.Object[])">
            <summary>
            调用服务方法
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.ConvertToUniUserQo(System.Object)">
            <summary>
            转换为 UniUserQo 对象
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.ConvertToUniGroupQo(System.Object)">
            <summary>
            转换为 UniGroupQo 对象
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.GenerateXmlRpcResponse(System.Object)">
            <summary>
            生成 XML-RPC 响应
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.GenerateXmlRpcValue(System.Object)">
            <summary>
            生成 XML-RPC 值
            </summary>
        </member>
        <member name="M:UwooAgent.Core.Middleware.XmlRpcMiddleware.GenerateXmlRpcFault(System.Int32,System.String)">
            <summary>
            生成 XML-RPC 错误响应
            </summary>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController">
            <summary>
            阅读理解智能体学生端控制器
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetStudentReadingTaskList(UwooAgent.Model.AI.StudentReadingTaskListInput)">
            <summary>
            获取学生的阅读理解任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetReadingTaskDetails(UwooAgent.Model.AI.StudentReadingTaskDetailsInput)">
            <summary>
            获取阅读理解任务详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.RecordVideoWatchStatus(UwooAgent.Model.AI.VideoWatchStatusInput)">
            <summary>
            记录视频观看状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.RecordVideoWatchDuration(UwooAgent.Model.AI.VideoWatchDurationInput)">
            <summary>
            记录视频观看时长
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.RecordDocumentReadStatus(UwooAgent.Model.AI.DocumentReadStatusInput)">
            <summary>
            记录文档阅读状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.SubmitMindMap(UwooAgent.Model.AI.MindMapSubmitInput)">
            <summary>
            提交思维导图任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.SubmitWordFillTask(UwooAgent.Model.AI.WordFillSubmitInput)">
            <summary>
            提交选词填空任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetStudentTaskHistory(UwooAgent.Model.AI.StudentTaskHistoryInput)">
            <summary>
            获取学生任务完成历史
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.ReadingDialogue(UwooAgent.Model.AI.ReadingDialogueInput,System.Threading.CancellationToken)">
            <summary>
            阅读理解情景对话
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.ReadingDialogueSubmit(UwooAgent.Model.AI.ReadingDialogueSubmitInput)">
            <summary>
            阅读理解情景对话提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.ReadingAssessment(UwooAgent.Model.AI.ReadingAssessmentInput)">
            <summary>
            阅读理解成果评估
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.ReadingKnowledge(UwooAgent.Model.AI.ReadingKnowledgeInput,System.Threading.CancellationToken)">
            <summary>
            阅读理解知识问答
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetStudentDoTaskResult(UwooAgent.Model.AI.GetStudentDoTaskResultInput)">
            <summary>
            获取学生做任务结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetTaskSubmitRecords(UwooAgent.Model.AI.GetTaskSubmitRecordsInput)">
            <summary>
            查询任务提交记录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.GetStudentNoStandardList(UwooAgent.Model.AI.GetStudentNoStandardListInput)">
            <summary>
            获取学生端未达标列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionStudentController.StudentSubmitNoStandardBackups(UwooAgent.Model.AI.StudentSubmitNoStandardBackupsInput)">
            <summary>
            学生端阅读理解阶段任务提交未达标对话记录备份
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController">
            <summary>
            阅读理解智能体教师端控制器
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.SaveReadingTask(UwooAgent.Model.AI.SaveReadingTaskInput)">
            <summary>
            保存阅读理解任务信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetReadingTaskDetails(UwooAgent.Model.AI.ReadingTaskDetailsInput)">
            <summary>
            获取阅读理解任务详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetProjectTaskDetails(UwooAgent.Model.AI.ProjectTaskDetailsInput)">
            <summary>
            获取阅读理解项目详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetReadingTaskList(UwooAgent.Model.AI.ReadingTaskListInput)">
            <summary>
            获取阅读理解任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetProjectTaskList(UwooAgent.Model.AI.GetProjectTaskListInput)">
            <summary>
            获取阅读理解项目列表（与项目化实践接口保持一致）
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.DeleteReadingTask(UwooAgent.Model.AI.DeleteReadingTaskInput)">
            <summary>
            删除阅读理解任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.PublishReadingTaskToClass(UwooAgent.Model.AI.PublishProjectTaskToClassInput)">
            <summary>
            发布阅读理解任务到班级
            </summary>
            <param name="input">发布参数</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.UnpublishProjectTask(UwooAgent.Model.AI.UnpublishProjectTaskInput)">
            <summary>
            撤销阅读理解任务发布
            </summary>
            <param name="input">撤销发布参数</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetStudentStatistics(UwooAgent.Model.AI.ReadingTaskStudentStatisticsInput)">
            <summary>
            获取学生完成情况统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetProjectSynthesizeAnalyse(UwooAgent.Model.AI.GetProjectSynthesizeAnalyseInput)">
            <summary>
            获取阅读理解项目综合分析
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetProjectStageTaskCount(UwooAgent.Model.AI.GetProjectStageTaskCountInput)">
            <summary>
            获取阅读理解项目阶段任务统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetProjectStudentCount(UwooAgent.Model.AI.GetProjectStudentCountInput)">
            <summary>
            获取阅读理解项目学生统计
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetStudentDoReadingTaskList(UwooAgent.Model.AI.GetStudentDoReadingTaskListInput)">
            <summary>
            获取学生做阅读理解任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.ValidateTaskByType(UwooAgent.Model.AI.ReadingProjectStageTaskInfoInput,System.String)">
            <summary>
            根据任务类型进行特定验证
            </summary>
            <param name="task"></param>
            <param name="stageName"></param>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.GetStudentSubmitContent(UwooAgent.Model.AI.GetStudentSubmitContentInput)">
            <summary>
            获取学生提交内容详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.ReadingDownloadStudentAchievement(UwooAgent.Model.AI.ReadingDownloadStudentAchievementInput)">
            <summary>
            教师端阅读理解下载学生成果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.AI.ReadingComprehensionTeacherController.ReadingDownloadStudentResult(UwooAgent.Model.AI.ReadingDownloadStudentResultInput)">
            <summary>
            教师端阅读理解下载学生评估结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub" -->
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.#ctor(Uwoo.Core.CacheManager.IService.ICacheService)">
            <summary>
            构造 注入
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.OnConnectedAsync">
            <summary>
            建立连接时异步触发
            </summary>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            离开连接时异步触发
            </summary>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.GetCnnectionIds(System.String)">
            <summary>
            根据用户名获取所有的客户端
            </summary>
            <param name="username"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.SendHomeMessage(System.String,System.String,System.String)">
            <summary>
            发送给指定的人
            </summary>
            <param name="username">sys_user表的登陆帐号</param>
            <param name="message">发送的消息</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.UserOffline">
            <summary>
            断开连接
            </summary>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.Login.LoginController">
            <summary>
            登录控制器
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Login.LoginController.SubmitLogin(Uwoo.Model.Login.SubmitLoginInput)">
            <summary>
            登录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Login.LoginController.xhunilogin(System.String,System.String)">
            <summary>
            徐汇统一登录回调
            </summary>
            <param name="sessid"></param>
            <param name="agentId"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Login.LoginController.UniLogin(UwooAgent.Model.UniLogin.UniLoginQo)">
            <summary>
            统一登录
            </summary>
            <param name="qo">临时授权码</param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController">
             <summary>
            1、普通参数校验只需要标识属性：[ObjectGeneralValidatorFilter(ValidatorGeneral.xxx,ValidatorGeneral.xxx)]，
            需要在ValidatorGeneral枚举中添加枚举值(参数名)，并在UseMethodsGeneralParameters方法中注入进去即可在任何地方重复使用
             
             2、model校验只需要标识属性[ObjectModelValidatorFilter(ValidatorModel.xxx)]
             需要在ValidatorModel枚举中添加枚举值(参数名)，
             并在UseMethodsModelParameters方法中注入进去(注入时可以指定需要验证的字段)即可在任何地方重复使用
             --如果其他方法使用的是同一个model，但验证的字段不同，在ValidatorModel重新添加一个枚举值，
             --并在UseMethodsModelParameters方法注入,添加新的指定字段即可
             </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test1(System.String,System.String)">
            <summary>
            验证UserName与PhoneNo为必填
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test2(System.String,System.String)">
            <summary>
            验证PhoneNo为必填
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test3(System.String,System.String)">
            <summary>
            验证字符长度与值大小
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test4(Uwoo.Entity.DomainModels.System.LoginInfo)">
            <summary>
            Login配置的规则用户名与密码必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test5(Uwoo.Entity.DomainModels.System.LoginInfo)">
            <summary>
            LoginOnlyPassWord配置的规则密码必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test6(Uwoo.Entity.DomainModels.System.LoginInfo,System.String)">
            <summary>
            同时验证实体LoginInfo与单个参数phoneNo
             Login配置的规则用户名与密码必填,手机号必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.OSS.AliOSSController">
            <summary>
            neuget包aliyun-net-sdk-core
            </summary>
        </member>
        <member name="T:Uwoo.WebApi.SwaggerDocTag">
            <summary>
            Swagger 注释帮助类
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.SwaggerDocTag.Apply(Microsoft.OpenApi.Models.OpenApiDocument,Swashbuckle.AspNetCore.SwaggerGen.DocumentFilterContext)">
            <summary>
            添加附加注释
            </summary>
            <param name="swaggerDoc"></param>
            <param name="context"></param>
        </member>
    </members>
</doc>
