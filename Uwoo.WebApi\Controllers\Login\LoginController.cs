using Coldairarrow.Util;
using Confluent.Kafka;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using Uwoo.Core.Attributes;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Entity.SystemModels.Enum;
using Uwoo.Model.Login;
using Uwoo.System.IServices;
using Uwoo.System.IServices.Login;
using Microsoft.Extensions.Options;
using UwooAgent.Model.UniLogin;
using Microsoft.Extensions.Configuration;
using Uwoo.Entity.DomainModels;
using Uwoo.Core.Extensions;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.System.IServices.AI;
using Uwoo.Core.Utilities;
using Dm.filter.log;
using Uwoo.Model;

namespace Uwoo.WebApi.Controllers.Login
{
    /// <summary>
    /// 登录控制器
    /// </summary>
    [Route("/Login/Login/[action]")]
    public class LoginController : ApiBaseController<ILoginService>
    {
        #region DI
        private readonly IOptions<XmlRpcClientOptions> _options;
        private readonly IConfiguration _config;
        private readonly ILoginService _loginService;
        private readonly IAgentCommonService  _commonservice;

        public LoginController(ILoginService loginService, IConfiguration config, IOptions<XmlRpcClientOptions> options, IAgentCommonService commonservice)
        {
            _loginService = loginService;
            _options = options;
            _config = config;
            _commonservice = commonservice;
        }
        #endregion

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public SubmitLoginOutput SubmitLogin([FromBody] SubmitLoginInput input)
        {
            if (string.IsNullOrEmpty(input.PhoneNo))
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "请输入手机号/用户名!"
                };
            }
            if (string.IsNullOrEmpty(input.Password))
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "请输入密码!"
                };
            }
            if(!input.RequestAgent.HasValue)
            {
                input.RequestAgent = RequestAgent;
            }
            return _loginService.SubmitLogin(input);
        }

        /// <summary>
        /// 徐汇统一登录回调
        /// </summary>
        /// <param name="sessid"></param>
        /// <param name="agentId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<ActionResult> xhunilogin(string sessid,string agentId)
        {
            var rpc_client_option = _options.Value;
            var baseurl = _config.GetValue<string>("BaseUrl");
            var redirect_url = $"{rpc_client_option.LoginUrl}?{rpc_client_option.CallbackUrl}";
            if (string.IsNullOrWhiteSpace(sessid))
            {
                return Redirect(redirect_url);
            }
            var uid = await _loginService.IsUserLogin(sessid);
            if (string.IsNullOrWhiteSpace(uid))
            {
                return Redirect(redirect_url);
            }
            var uni_user_info = await _loginService.GetUserByUidAsync(uid);
            if (uni_user_info == null)
            {
                return Redirect(redirect_url);
            }
            var user = await _loginService.GetTokenInfoByUniUid(uni_user_info.UserId);
            if (user == null)
            {
                var xpassword = "123456";
                user = new Base_User
                {
                    Id = IdHelper.GetId(),
                    Password = xpassword.ToMD5String16(),
                    PhoneNum = uni_user_info.UserName ?? uni_user_info.NickName,
                    State = 1,
                    CreateTime = DateTime.Now,
                    UserName = uni_user_info.NickName ?? uni_user_info.UserId,
                    Deleted = false,
                    XhId= uni_user_info.UserId,
                    RealName = uni_user_info.UserName ?? uni_user_info.NickName,
                    CreatorId = "Admin"
                };
                var role = "";
                if (uni_user_info.UserType == "10010")
                {
                    role = "1243404546835746816";
                }
                else if (uni_user_info.UserType == "10100")
                {
                    role = "1243404500610322432";
                }
                Base_UserRole base_UserRole = new Base_UserRole();
                base_UserRole.Id = IdHelper.GetId();
                base_UserRole.CreateTime = DateTime.Now;
                base_UserRole.CreatorId = "Admin";
                base_UserRole.RoleId = role;
                base_UserRole.UserId = user.Id;
               
                //    else if (uni_user_info.UserType == "11000")
                //    {
                //        user.Role = UserRoleEnum.AreaAdmin;
                //    }
                //    else if (uni_user_info.UserType == "11100")
                //    {
                //        user.Role = UserRoleEnum.SchoolAdmin;
                //    }
                //    token_info = new UniUserTokenInfo
                //    {
                //        Oid = Guid.NewGuid(),
                //        UserId = user.Oid.ToString(),
                //        UserName = uni_user_info.UserName,
                //        TokenId = uni_user_info.UserId
                //    };

                await _loginService.AddorUpdateXHUser(user,base_UserRole, uni_user_info.GroupId);
                //    await _uni_token_user_service.Value.AddAsync(token_info);
                //}
                //else
                //{
                //    var user = _user_service.Value.Load(Guid.Parse(token_info.UserId));
                //    {
                //        if (user == null)
                //        {
                //            var xsalt = EncryptUtil.GetSalt();
                //            //密码需要添加注册时的随机盐值
                //            var xpassword = "123456" + xsalt;
                //            user = new UserInfo
                //            {
                //                Oid = Guid.Parse(token_info.UserId),
                //                Password = EncryptUtil.GenerateStorngPassword(xpassword),
                //                Mobile = "",
                //                Avatar = $"{baseurl}assets/default.png",
                //                Email = uni_user_info.Email ?? "",
                //                Grade = "",
                //                School = null,
                //                Level = 0,
                //                Point = 0,
                //                Role = UserRoleEnum.Student,
                //                Salt = xsalt,
                //                State = UserStatusEnum.Audited,
                //                AddTime = DateTime.Now,
                //                UserName = uni_user_info.NickName ?? uni_user_info.UserId,
                //                IsAdmin = false,
                //                IsDelete = false,
                //                RealName = uni_user_info.UserName ?? uni_user_info.NickName,
                //                CreatorId = Guid.Parse("336a10d9-3be5-4976-9bba-3b8dbd94bc15"),
                //                AccumAnswerTime = 0,
                //                AccumAnswerCount = 0,
                //                AccumViewCount = 0,
                //                AccumViewTime = 0,
                //                AccurateRate = 0,
                //                Source = UserSourceEnum.UniLogin
                //            };
                //            if (uni_user_info.UserType == "10010")
                //            {
                //                user.Role = UserRoleEnum.Student;
                //            }
                //            else if (uni_user_info.UserType == "10100")
                //            {
                //                user.Role = UserRoleEnum.Teacher;
                //            }
                //            else if (uni_user_info.UserType == "11000")
                //            {
                //                user.Role = UserRoleEnum.AreaAdmin;
                //            }
                //            else if (uni_user_info.UserType == "11100")
                //            {
                //                user.Role = UserRoleEnum.SchoolAdmin;
                //            }
                //            await _user_service.Value.AddorUpdateXHUser(user, uni_user_info.GroupId);
                //        }
                //    }
                //    if (user.Role == UserRoleEnum.Student && uni_user_info.UserType == "10100")
                //    {
                //        user.Role = UserRoleEnum.Teacher;
                //        await _user_service.Value.AddorUpdateXHUser(user, uni_user_info.GroupId);
                //    }
                //    else
                //    {
                //        await _user_service.Value.AddorUpdateXHUser(new UserInfo { Oid = Guid.Parse(token_info.UserId), Role = user.Role }, uni_user_info.GroupId);
                //    }
                //}
            }
            var addressUrl = "";
            if (!agentId.IsNullOrEmpty())
            {
                var agentInfo = _commonservice.GetAgentInfoByAgentCode(agentId);
                switch (agentInfo.AgentBotCode)
                {
                    case "Agent_TeachingPlan":
                        addressUrl = "teaching-plan/home?agentId=1947937419731288064";
                        break;
                    case "Agent_PPT":
                        addressUrl = "ai-ppt/home?agentId=1961011573967699968";
                        break;
                    case "Agent_Question":
                        addressUrl = "questions?agentId=1948735067920941056";
                        break;
                    case "Agent_TeachingPlanEvaluate":
                        addressUrl = "teaching-plan-evaluation?agentId=1957275859350339584";
                        break;
                    default:
                        break;
                }
            }
            //
            var tempid = $"{RedisKeys.UNI_LOGIN_CODE}{Guid.NewGuid():N}";
            await RedisHelper.SetAsync(tempid, user.XhId, TimeSpan.FromSeconds(120));
            var xurl = $"{baseurl}{addressUrl}&tempid={tempid}";
            return Redirect(xurl);
        }

        /// <summary>
        /// 统一登录
        /// </summary>
        /// <param name="qo">临时授权码</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        public async Task<AjaxResult<LoginVo>> UniLogin(UniLoginQo qo)
        {
            if (string.IsNullOrWhiteSpace(qo.TempId))
            {
                return Error<LoginVo>(null,"授权码不能为空");
            }

            var isexist = await RedisHelper.ExistsAsync(qo.TempId);
            if (!isexist)
            {
                return Error<LoginVo>(null, "用户信息不存在");
            }

            return await _loginService.UniLogin(qo.TempId);
        }
    }
}
