using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using UwooAgent.Model.RpcModel;

namespace XmlRpcTest
{
    class Program
    {
        static void Main(string[] args)
        {
            // 模拟从 XML-RPC 解析出来的字典数据
            var dict = new Dictionary<string, object>
            {
                ["user_id"] = "fyzx_wangyf",
                ["group_id"] = 11718,
                ["user_name"] = "王瑜凡",
                ["nickname"] = "王瑜凡",
                ["gender"] = "女",
                ["email"] = "",
                ["active_flag"] = 1,
                ["admin_flag"] = 0,
                ["user_type"] = "10100",
                ["password"] = "h5@Lp3*Xv8"
            };

            Console.WriteLine("原始字典数据:");
            foreach (var kvp in dict)
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value}");
            }

            // 测试原来的方法（会失败）
            Console.WriteLine("\n=== 测试原来的 JSON 序列化方法 ===");
            try
            {
                var json = JsonConvert.SerializeObject(dict);
                Console.WriteLine($"序列化后的 JSON: {json}");

                var userQoOld = JsonConvert.DeserializeObject<UniUserQo>(json);
                Console.WriteLine($"反序列化结果:");
                Console.WriteLine($"  UserId: {userQoOld.UserId}");
                Console.WriteLine($"  UserType: {userQoOld.UserType}");
                Console.WriteLine($"  GroupId: {userQoOld.GroupId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"原方法失败: {ex.Message}");
            }

            // 测试新的手动映射方法
            Console.WriteLine("\n=== 测试新的手动映射方法 ===");
            var userQoNew = ConvertToUniUserQo(dict);
            Console.WriteLine($"手动映射结果:");
            Console.WriteLine($"  UserId: {userQoNew.UserId}");
            Console.WriteLine($"  UserType: {userQoNew.UserType}");
            Console.WriteLine($"  GroupId: {userQoNew.GroupId}");
            Console.WriteLine($"  UserName: {userQoNew.UserName}");
            Console.WriteLine($"  Gender: {userQoNew.Gender}");
            Console.WriteLine($"  ActiveFlag: {userQoNew.ActiveFlag}");
            Console.WriteLine($"  AdminFlag: {userQoNew.AdminFlag}");
        }

        private static UniUserQo ConvertToUniUserQo(Dictionary<string, object> dict)
        {
            var userQo = new UniUserQo();

            if (dict.TryGetValue("user_id", out var userId))
                userQo.UserId = userId?.ToString();

            if (dict.TryGetValue("group_id", out var groupId))
                userQo.GroupId = Convert.ToInt32(groupId);

            if (dict.TryGetValue("user_name", out var userName))
                userQo.UserName = userName?.ToString();

            if (dict.TryGetValue("nickname", out var nickName))
                userQo.NickName = nickName?.ToString();

            if (dict.TryGetValue("birthday", out var birthday))
                userQo.Birthday = birthday?.ToString();

            if (dict.TryGetValue("gender", out var gender))
                userQo.Gender = gender?.ToString();

            if (dict.TryGetValue("email", out var email))
                userQo.Email = email?.ToString();

            if (dict.TryGetValue("active_flag", out var activeFlag))
                userQo.ActiveFlag = Convert.ToInt32(activeFlag);

            if (dict.TryGetValue("admin_flag", out var adminFlag))
                userQo.AdminFlag = Convert.ToInt32(adminFlag);

            if (dict.TryGetValue("user_type", out var userType))
                userQo.UserType = userType?.ToString();

            if (dict.TryGetValue("xhjw_user_id", out var jwUserId))
                userQo.JwUserId = Convert.ToInt32(jwUserId);

            return userQo;
        }
    }
}
